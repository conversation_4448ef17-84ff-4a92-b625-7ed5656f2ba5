import { useRef, useState } from "react";
import {
    Button,
    Modal,
    Form,
    Input,
    Popconfirm,
    Breadcrumb,
    Row,
    Col,
    Tag,
    Select,
    DatePicker,
    InputNumber,
    Card,
    Space,
    Divider
} from "antd";
import {EditOutlined, DeleteOutlined, EyeOutlined, PlusOutlined, MinusCircleOutlined} from "@ant-design/icons";
import ProTable, { ActionType } from "@ant-design/pro-table";
import {Link} from "react-router-dom";
import {useTranslation} from "react-i18next";
import {toast} from "react-toastify";
import {useDispatch} from "react-redux";
import {
    deleteIncrease,
    getIncreases,
    storeIncrease,
    updateIncrease
} from "../../../features/admin/increaseSlice.ts";
import { hasPermission } from "../../../helpers/permissions.ts";
import dayjs from "dayjs";

const ManageIncreases = () => {
    const {t} = useTranslation();
    const dispatch = useDispatch();
    const actionRef = useRef<ActionType>();
    const [form] = Form.useForm();
    const [modalVisible, setModalVisible] = useState(false);
    type IncreaseValue = {
        id?: number;
        increase_id?: number;
        percentage: number;
        date_subscription: string;
        date_website: string;
        created_at?: string;
        updated_at?: string;
    };

    type Increase = {
        id: number;
        nom_fr: string;
        nom_en: string;
        nom_ar: string;
        status: number;
        current_value_subscription?: IncreaseValue;
        current_value_website?: IncreaseValue;
        increase_values?: IncreaseValue[];
        lines_count?: number;
        created_at?: string;
        updated_at?: string;
        [key: string]: any;
    };
    const [editingIncrease, setEditingIncrease] = useState<Increase | null>(null);
    const [viewMode, setViewMode] = useState(false);
    const [loading, setLoading] = useState(false);
    const [pageNumber, setPageNumber] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    const [total, setTotal] = useState(0);

    /*|--------------------------------------------------------------------------
    |  - HANDLE GET INCREASES
    |-------------------------------------------------------------------------- */
    const handleGetIncreases = async (params: any, sort: any, filter: any) => {
        try {
            const response: any = await dispatch(
                getIncreases({
                    pageNumber,
                    perPage: pageSize,
                    params,
                    sort,
                    filter,
                })
            ).unwrap();
            setTotal(response.total);
            return response.data;
        } catch (error: any) {
            console.error("Error fetching increases:", error);
            return [];
        }
    };

    /*|--------------------------------------------------------------------------
    |  - COLUMNS
    |-------------------------------------------------------------------------- */
    const columns: any = [
        {
            title: t("manage_increases.labels.name_fr"),
            dataIndex: "nom_fr",
            key: "nom_fr",
            search: {
                transform: (value: any) => ({ nom_fr: value }),
            },
            render: (text: any) => <span className="font-medium">{text}</span>,
        },
        {
            title: t("manage_increases.labels.name_en"),
            dataIndex: "nom_en",
            key: "nom_en",
            search: {
                transform: (value: any) => ({ nom_en: value }),
            },
        },
        {
            title: t("manage_increases.labels.name_ar"),
            dataIndex: "nom_ar",
            key: "nom_ar",
            search: {
                transform: (value: any) => ({ nom_ar: value }),
            },
        },
        {
            title: t("manage_increases.labels.percentage"),
            key: "percentage",
            search: {
                transform: (value: any) => ({ percentage: value }),
            },
            render: (_: any, record: any) => {
                const current = record.current_value_subscription;
                return current ? <span>{current.percentage}%</span> : "-";
            },
        },
        {
            title: t("manage_increases.labels.date_subscription"),
            key: "date_subscription",
            search: false,
            render: (_: any, record: any) => {
                const current = record.current_value_subscription;
                return current && current.date_subscription
                    ? dayjs(current.date_subscription).format("DD/MM/YYYY")
                    : "-";
            },
        },
        {
            title: t("manage_increases.labels.date_website"),
            key: "date_website",
            search: false,
            render: (_: any, record: any) => {
                const current = record.current_value_subscription;
                return current && current.date_website
                    ? dayjs(current.date_website).format("DD/MM/YYYY")
                    : "-";
            },
        },
        {
            title: t("manage_increases.labels.status"),
            dataIndex: "status",
            key: "status",
            search: {
                transform: (value: any) => ({ status: value }),
            },
            valueType: "select",
            valueEnum: {
                1: { text: t("manage_increases.status.active"), status: "Success" },
                0: { text: t("manage_increases.status.inactive"), status: "Error" },
            },
            render: (_: any, record: any) => (
                <Tag color={record.status === 1 ? "green" : "red"}>
                    {record.status === 1 ? t("manage_increases.status.active") : t("manage_increases.status.inactive")}
                </Tag>
            ),
        },
        {
            title: t("manage_increases.labels.createdAt"),
            dataIndex: "created_at",
            key: "created_at",
            search: false,
            render: (text: any) => dayjs(text).format("DD/MM/YYYY HH:mm"),
        },
        {
            title: t("manage_increases.labels.actions"),
            fixed: "right" as const,
            width: 170,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <div className="flex gap-1">
                    <Button
                        className="btn-view"
                        icon={<EyeOutlined />}
                        onClick={() => handleView(record)}
                    />
                    {hasPermission("edit_increases") && (
                        <Button
                            className="btn-edit"
                            icon={<EditOutlined />}
                            onClick={() => handleEdit(record)}
                        />
                    )}
                    {hasPermission("delete_increases") && (
                        <Popconfirm
                            title={t("manage_increases.confirmDelete")}
                            onConfirm={() => handleDelete(record.id)}
                            okText={t("common.yes")}
                            cancelText={t("common.no")}
                        >
                            <Button className="btn-delete" icon={<DeleteOutlined />} danger />
                        </Popconfirm>
                    )}
                </div>
            ),
        },
    ];

    const breadcrumbItems = [
        {
            title: <Link className="!bg-white" to="/auth/commercial-dashboard">{t("auth_sidebar.categories.transport")}</Link>,
        },
        {
            title: t("manage_increases.title"),
        },
    ];

    /*|--------------------------------------------------------------------------
    |  - HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
    const handleView = (record: any) => {
        setEditingIncrease(record);

        const formattedIncreaseValues = record.increase_values?.map((value: any) => ({
            ...value,
            date_subscription: value.date_subscription ? dayjs(value.date_subscription) : null,
            date_website: value.date_website ? dayjs(value.date_website) : null
        })) || [];

        form.setFieldsValue({
            ...record,
            increase_values: formattedIncreaseValues
        });
        setViewMode(true);
        setModalVisible(true);
    };

    const handleEdit = (record: any) => {
        setEditingIncrease(record);

        const formattedIncreaseValues = record.increase_values?.map((value: any) => ({
            ...value,
            date_subscription: value.date_subscription ? dayjs(value.date_subscription) : null,
            date_website: value.date_website ? dayjs(value.date_website) : null
        })) || [];

        form.setFieldsValue({
            ...record,
            increase_values: formattedIncreaseValues
        });
        setViewMode(false);
        setModalVisible(true);
    };

    const handleAdd = () => {
        setEditingIncrease(null);
        form.resetFields();
        // Initialize with one empty increase value
        form.setFieldsValue({
            increase_values: [{ percentage: '', date_subscription: null, date_website: null }]
        });
        setViewMode(false);
        setModalVisible(true);
    };

    const handleDelete = async (id: any) => {
        const toastId = toast.loading(t("messages.loading"));
        try {
            await dispatch(deleteIncrease(id)).unwrap();
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000,
            });
            actionRef.current?.reload();
        } catch (error: any) {
            toast.update(toastId, {
                render: error || t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000,
            });
        }
    };

    const handleReset = () => {
        setModalVisible(false);
        setEditingIncrease(null);
        setViewMode(false);
        form.resetFields();
    };

    const handleSubmit = async () => {
        try {
            const values = await form.validateFields();
            const toastId = toast.loading(t("messages.loading"));

            const formattedIncreaseValues = values.increase_values?.map((value: any) => ({
                percentage: Number(value.percentage),
                date_subscription: value.date_subscription ? value.date_subscription.format('YYYY-MM-DD') : null,
                date_website: value.date_website ? value.date_website.format('YYYY-MM-DD') : null
            })) || [];

            const payload = {
                nom_fr: values.nom_fr,
                nom_en: values.nom_en,
                nom_ar: values.nom_ar,
                status: values.status,
                increase_values: formattedIncreaseValues
            };

            if (editingIncrease) {
                await dispatch(updateIncrease({ id: editingIncrease.id, ...payload })).unwrap();
            } else {
                await dispatch(storeIncrease(payload)).unwrap();
            }

            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000,
            });
            actionRef.current?.reload();
            handleReset();
        } catch (error: any) {
            const fieldErrors: any = Object.keys(error.errors || {})?.map(
                (field: any) => ({
                    name: field,
                    errors: [error.errors[field][0]],
                })
            );
            form.setFields(fieldErrors);
        }
    };

    return (
        <div className="p-6">
            <Breadcrumb items={breadcrumbItems} className="mb-4" />

            <Row>
                <Col span={24}>
                    <ProTable
                        headerTitle={t("manage_increases.title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        request={async (params: any, sort: any, filter: any) => {
                            setLoading(true);
                            const dataFilter:any = await handleGetIncreases(params, sort, filter);
                            setLoading(false);
                            return {
                                data: dataFilter,
                                success: true,
                            };
                        }}
                        rowKey={"id"}
                        sortDirections={["ascend", "descend"]}
                        pagination={{
                            pageSize,
                            total,
                            showSizeChanger: true,
                            onChange: (page) => setPageNumber(page),
                            onShowSizeChange: (_, pageSize) => {
                                setPageSize(pageSize);
                            },
                        }}
                        scroll={{
                            x: 800,
                        }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        options={{
                            fullScreen: true,
                        }}
                        toolBarRender={() => [
                            hasPermission("create_increases") && (
                                <Button
                                    key="add"
                                    type="primary"
                                    onClick={handleAdd}
                                >
                                    {t("manage_increases.add")}
                                </Button>
                            ),
                        ]}
                    />
                </Col>
            </Row>

            <Modal
                title={
                    viewMode
                        ? t("manage_increases.details")
                        : editingIncrease
                        ? t("manage_increases.edit")
                        : t("manage_increases.add")
                }
                open={modalVisible}
                onCancel={handleReset}
                footer={
                    viewMode
                        ? [
                              <Button key="close" onClick={handleReset}>
                                  {t("common.close")}
                              </Button>,
                          ]
                        : [
                              <Button key="cancel" onClick={handleReset}>
                                  {t("common.cancel")}
                              </Button>,
                              <Button key="submit" type="primary" onClick={handleSubmit}>
                                  {t("manage_increases.save")}
                              </Button>,
                          ]
                }
                width={800}
            >
                {!viewMode ? (
                    <Form form={form} layout="vertical" disabled={viewMode}>
                        <Row gutter={16}>
                            <Col span={8}>
                                <Form.Item
                                    name="nom_fr"
                                    label={t("manage_increases.labels.name_fr")}
                                    rules={[
                                        {
                                            required: true,
                                            message: t("manage_increases.errors.nameFrRequired"),
                                        },
                                    ]}
                                >
                                    <Input placeholder={t("manage_increases.placeholders.name_fr")} />
                                </Form.Item>
                            </Col>
                            <Col span={8}>
                                <Form.Item
                                    name="nom_en"
                                    label={t("manage_increases.labels.name_en")}
                                    rules={[
                                        {
                                            required: true,
                                            message: t("manage_increases.errors.nameEnRequired"),
                                        },
                                    ]}
                                >
                                    <Input placeholder={t("manage_increases.placeholders.name_en")} />
                                </Form.Item>
                            </Col>
                            <Col span={8}>
                                <Form.Item
                                    name="nom_ar"
                                    label={t("manage_increases.labels.name_ar")}
                                    rules={[
                                        {
                                            required: true,
                                            message: t("manage_increases.errors.nameArRequired"),
                                        },
                                    ]}
                                >
                                    <Input placeholder={t("manage_increases.placeholders.name_ar")} />
                                </Form.Item>
                            </Col>
                        </Row>
                        <Row gutter={16}>
                            <Col span={12}>
                                <Form.Item
                                    name="status"
                                    label={t("manage_increases.labels.status")}
                                    rules={[
                                        {
                                            required: true,
                                            message: t("manage_increases.errors.statusRequired"),
                                        },
                                    ]}
                                >
                                    <Select placeholder={t("manage_increases.placeholders.status")}>
                                        <Select.Option value={1}>{t("manage_increases.status.active")}</Select.Option>
                                        <Select.Option value={0}>{t("manage_increases.status.inactive")}</Select.Option>
                                    </Select>
                                </Form.Item>
                            </Col>
                        </Row>
                        <Row gutter={16}>
                            <Col xs={24}>
                                <Form.Item
                                    label={t("manage_increases.labels.increase_values")}
                                    required
                                >
                                    <Form.List
                                        name="increase_values"
                                        rules={[
                                            {
                                                validator: async (_, increaseValues) => {
                                                    if (!increaseValues || increaseValues.length < 1) {
                                                        return Promise.reject(new Error('Au moins une valeur d\'augmentation est requise'));
                                                    }
                                                },
                                            },
                                        ]}
                                    >
                                        {(fields, { add, remove }, { errors }) => (
                                            <>
                                                {fields.map(({ key, name, ...restField }) => (
                                                    <Card
                                                        key={key}
                                                        size="small"
                                                        className="mb-4"
                                                        title={`Valeur ${name + 1}`}
                                                        extra={
                                                            fields.length > 1 && !viewMode ? (
                                                                <Button
                                                                    type="link"
                                                                    danger
                                                                    icon={<MinusCircleOutlined />}
                                                                    onClick={() => remove(name)}
                                                                >
                                                                    Supprimer
                                                                </Button>
                                                            ) : null
                                                        }
                                                    >
                                                        <Row gutter={16}>
                                                            <Col xs={24} sm={8}>
                                                                <Form.Item
                                                                    {...restField}
                                                                    name={[name, 'percentage']}
                                                                    label={t("manage_increases.labels.percentage")}
                                                                    rules={[
                                                                        { required: true, message: t("manage_increases.errors.percentageRequired") }
                                                                    ]}
                                                                >
                                                                    <InputNumber
                                                                        placeholder={t("manage_increases.placeholders.percentage")}
                                                                        style={{ width: "100%" }}
                                                                        min={0}
                                                                        max={100}
                                                                        step={0.1}
                                                                        disabled={viewMode}
                                                                    />
                                                                </Form.Item>
                                                            </Col>
                                                            <Col xs={24} sm={8}>
                                                                <Form.Item
                                                                    {...restField}
                                                                    name={[name, 'date_subscription']}
                                                                    label={t("manage_increases.labels.date_subscription")}
                                                                    rules={[
                                                                        { required: true, message: t("manage_increases.errors.dateSubscriptionRequired") }
                                                                    ]}
                                                                >
                                                                    <DatePicker
                                                                        style={{ width: '100%' }}
                                                                        format="YYYY-MM-DD"
                                                                        disabled={viewMode}
                                                                        placeholder={t("manage_increases.placeholders.date_subscription")}
                                                                    />
                                                                </Form.Item>
                                                            </Col>
                                                            <Col xs={24} sm={8}>
                                                                <Form.Item
                                                                    {...restField}
                                                                    name={[name, 'date_website']}
                                                                    label={t("manage_increases.labels.date_website")}
                                                                    rules={[
                                                                        { required: true, message: t("manage_increases.errors.dateWebsiteRequired") }
                                                                    ]}
                                                                >
                                                                    <DatePicker
                                                                        style={{ width: '100%' }}
                                                                        format="YYYY-MM-DD"
                                                                        disabled={viewMode}
                                                                        placeholder={t("manage_increases.placeholders.date_website")}
                                                                    />
                                                                </Form.Item>
                                                            </Col>
                                                        </Row>
                                                    </Card>
                                                ))}
                                                {!viewMode && (
                                                    <Form.Item>
                                                        <Button
                                                            type="dashed"
                                                            onClick={() => add()}
                                                            block
                                                            icon={<PlusOutlined />}
                                                        >
                                                            Ajouter une valeur d'augmentation
                                                        </Button>
                                                        <Form.ErrorList errors={errors} />
                                                    </Form.Item>
                                                )}
                                            </>
                                        )}
                                    </Form.List>
                                </Form.Item>
                            </Col>
                        </Row>
                    </Form>
                ) : (
                    <div className="p-4">
                        <Row gutter={[16, 16]}>
                            <Col xs={24} sm={8}>
                                <div className="mb-4">
                                    <div className="text-sm font-medium text-gray-500 mb-1">
                                        {t("manage_increases.labels.name_fr")}
                                    </div>
                                    <div className="bg-blue-50 text-blue-700 px-3 py-2 rounded-md text-base font-medium">
                                        {editingIncrease?.nom_fr || "-"}
                                    </div>
                                </div>
                            </Col>
                            <Col xs={24} sm={8}>
                                <div className="mb-4">
                                    <div className="text-sm font-medium text-gray-500 mb-1">
                                        {t("manage_increases.labels.name_en")}
                                    </div>
                                    <div className="bg-blue-50 text-blue-700 px-3 py-2 rounded-md text-base font-medium">
                                        {editingIncrease?.nom_en || "-"}
                                    </div>
                                </div>
                            </Col>
                            <Col xs={24} sm={8}>
                                <div className="mb-4">
                                    <div className="text-sm font-medium text-gray-500 mb-1">
                                        {t("manage_increases.labels.name_ar")}
                                    </div>
                                    <div className="bg-blue-50 text-blue-700 px-3 py-2 rounded-md text-base font-medium">
                                        {editingIncrease?.nom_ar || "-"}
                                    </div>
                                </div>
                            </Col>
                        </Row>
                        <Row gutter={[16, 16]}>
                            <Col xs={24} sm={12}>
                                <div className="mb-4">
                                    <div className="text-sm font-medium text-gray-500 mb-1">
                                        {t("manage_increases.labels.status")}
                                    </div>
                                    <div className="bg-blue-50 text-blue-700 px-3 py-2 rounded-md text-base font-medium">
                                        <Tag color={editingIncrease?.status === 1 ? "green" : "red"}>
                                            {editingIncrease?.status === 1 ? t("manage_increases.status.active") : t("manage_increases.status.inactive")}
                                        </Tag>
                                    </div>
                                </div>
                            </Col>
                        </Row>
                        <Divider orientation="left">{t("manage_increases.labels.increase_values")}</Divider>
                        {editingIncrease?.increase_values?.map((value: any, index: number) => (
                            <Card key={index} size="small" className="mb-4" title={`Valeur ${index + 1}`}>
                                <Row gutter={16}>
                                    <Col xs={24} sm={8}>
                                        <div className="mb-4">
                                            <div className="text-sm font-medium text-gray-500 mb-1">
                                                {t("manage_increases.labels.percentage")}
                                            </div>
                                            <div className="bg-gray-50 text-gray-700 px-3 py-2 rounded-md">
                                                {value.percentage}%
                                            </div>
                                        </div>
                                    </Col>
                                    <Col xs={24} sm={8}>
                                        <div className="mb-4">
                                            <div className="text-sm font-medium text-gray-500 mb-1">
                                                {t("manage_increases.labels.date_subscription")}
                                            </div>
                                            <div className="bg-gray-50 text-gray-700 px-3 py-2 rounded-md">
                                                {value.date_subscription ? dayjs(value.date_subscription).format("DD/MM/YYYY") : "-"}
                                            </div>
                                        </div>
                                    </Col>
                                    <Col xs={24} sm={8}>
                                        <div className="mb-4">
                                            <div className="text-sm font-medium text-gray-500 mb-1">
                                                {t("manage_increases.labels.date_website")}
                                            </div>
                                            <div className="bg-gray-50 text-gray-700 px-3 py-2 rounded-md">
                                                {value.date_website ? dayjs(value.date_website).format("DD/MM/YYYY") : "-"}
                                            </div>
                                        </div>
                                    </Col>
                                </Row>
                            </Card>
                        ))}
                    </div>
                )}
            </Modal>
        </div>
    );
};

export default ManageIncreases;
